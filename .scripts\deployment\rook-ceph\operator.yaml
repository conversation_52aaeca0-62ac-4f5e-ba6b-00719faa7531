apiVersion: apps/v1
kind: Deployment
metadata:
  name: rook-ceph-operator
  namespace: rook-ceph
  labels:
    app: rook-ceph-operator
spec:
  selector:
    matchLabels:
      app: rook-ceph-operator
  replicas: 1
  template:
    metadata:
      labels:
        app: rook-ceph-operator
    spec:
      serviceAccountName: rook-ceph-system
      containers:
      - name: rook-ceph-operator
        image: rook/ceph:v1.12.3
        args: ["ceph", "operator"]
        env:
        - name: ROOK_LOG_LEVEL
          value: "INFO"
        - name: ROOK_CEPH_STATUS_CHECK_INTERVAL
          value: "60s"
        - name: ROOK_MON_HEALTHCHECK_INTERVAL
          value: "45s"
        - name: ROOK_MON_OUT_TIMEOUT
          value: "600s"
        - name: ROOK_DISCOVER_DEVICES_INTERVAL
          value: "60m"
        - name: ROOK_HOSTPATH_REQUIRES_PRIVILEGED
          value: "true"
        - name: R<PERSON><PERSON>_CSI_ENABLE_CEPHFS
          value: "true"
        - name: <PERSON><PERSON><PERSON>_CSI_ENABLE_RBD
          value: "true"
        - name: RO<PERSON>_CSI_ENABLE_GRPC_METRICS
          value: "true"
        - name: CSI_PROVISIONER_TOLERATIONS
          value: "- key: node.kubernetes.io/unreachable\n  operator: Exists\n  effect: NoExecute\n  tolerationSeconds: 5"
        - name: ROOK_ALLOW_MULTIPLE_FILESYSTEMS
          value: "true"
        - name: ROOK_CSI_ALLOW_UNSUPPORTED_VERSION
          value: "true"
        - name: ROOK_ENABLE_DISCOVERY_DAEMON
          value: "false"
        - name: POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        resources:
          limits:
            cpu: "500m"
            memory: "512Mi"
          requests:
            cpu: "100m"
            memory: "128Mi"
        volumeMounts:
        - mountPath: /var/lib/rook
          name: rook-config
        - mountPath: /etc/ceph
          name: default-config-dir
        - mountPath: /dev
          name: devices
        - mountPath: /sys
          name: sys
        - mountPath: /lib/modules
          name: lib-modules
          readOnly: true
      volumes:
      - name: rook-config
        emptyDir: {}
      - name: default-config-dir
        emptyDir: {}
      - name: devices
        hostPath:
          path: /dev
      - name: sys
        hostPath:
          path: /sys
      - name: lib-modules
        hostPath:
          path: /lib/modules
