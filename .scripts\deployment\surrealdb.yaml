
apiVersion: apps/v1
kind: Deployment
metadata:
  name: surrealdb
  namespace: surreal
  labels:
    app: surrealdb
spec:
  replicas: 1
  selector:
    matchLabels:
      app: surrealdb
  template:
    metadata:
      labels:
        app: surrealdb
    spec:
      containers:
      - name: surrealdb
        image: surrealdb/surrealdb:latest
        ports:
        - containerPort: 8000
        command:
        - /surreal
        args:
        - start
        - --user
        - root
        - --pass
        - PncGD51LsEXBHtdH73FbclMJxcYuAk6Y
        - --log=debug
        - tikv://tikv-pd:2379
        resources:
          limits:
            cpu: 1
            memory: 1Gi
          requests:
            cpu: 500m
            memory: 500Mi

---
apiVersion: v1
kind: Service
metadata:
  name: surrealdb
  namespace: surreal
spec:
  selector:
    app: surrealdb
  ports:
  - port: 8000
    targetPort: 8000
  type: LoadBalancer
