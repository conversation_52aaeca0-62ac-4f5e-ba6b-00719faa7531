import type { ServerWebSocket } from "bun";
import { WSHand<PERSON>, type ServerWSData } from "../../core/ws.handler/ws.handler";

/**
 * Generic WS Handler, just forward messages
 */
export class HGeneric extends WSHandler {
    constructor(ws: ServerWebSocket<ServerWSData>) {
        super(ws);
    }

    async onMessage(type: number, args: any[]) {
        // console.log("on message", type, args);
    }
}
