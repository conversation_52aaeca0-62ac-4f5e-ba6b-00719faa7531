import { gatewayStore } from "../../../../instance";
import { $fn } from "../../../core/fn";

// ------------------------ switch
$fn(
    "iot.sw.state.set",
    async function (switchId: string, pin: number, state: boolean) {
        // store device state
        await gatewayStore.sessionSet({
            id: switchId,
            [`pin_${pin}`]: state,
        });

        // send command
        this.invokePeer(switchId, "sw", [pin, state]);

        // put log
        const userName = await gatewayStore.userGetName(this.user.uid);
        const deviceName = await gatewayStore.deviceGetName(switchId);
        if (state) {
            await gatewayStore.msgPut({
                rid: switchId,
                deviceId: switchId,
                uid: this.user.uid,
                txt: `${userName} turn ${deviceName} on `,
            });
        } else {
            await gatewayStore.msgPut({
                rid: switchId,
                deviceId: switchId,
                uid: this.user.uid,
                txt: `${userName} turn ${deviceName} off `,
            });
        }
    },
);

$fn("iot.sw.state.get", async function (switchId: string, pin: number) {
    const session = await gatewayStore.sessionGet(switchId);
    return session?.[`pin_${pin}`] as boolean;
});
