apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: tidbclusters.pingcap.com
spec:
  group: pingcap.com
  versions:
  - name: v1alpha1
    served: true
    storage: true
    schema:
      openAPIV3Schema:
        type: object
        properties:
          spec:
            type: object
  scope: Namespaced
  names:
    plural: tidbclusters
    singular: tidbcluster
    kind: TidbCluster
    shortNames:
    - tc
