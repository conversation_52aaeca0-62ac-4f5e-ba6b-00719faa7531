import type { IOTDoorState } from "cm_net";
import { gatewayStore } from "../../../../instance";
import { $fn } from "../../../core/fn";

// ------------------------ gate
const DOOR_PIN = 1;
$fn("iot.door.state.set", async function (doorId: string, state: IOTDoorState) {
    // store device state
    await gatewayStore.sessionSet({
        id: doorId,
        state: state,
    });

    // send command
    if (state === "open") {
        await this.invokePeer(doorId, "sw", [DOOR_PIN, true]);
    } else if (state === "close") {
        await this.invokePeer(doorId, "sw", [DOOR_PIN, false]);
    } else if (state === "open_auto") {
        await this.invokePeer(doorId, "btn", [DOOR_PIN, 10000]);
    }

    // put log
    const userName = await gatewayStore.userGetName(this.user.uid);
    const deviceName = await gatewayStore.deviceGetName(doorId);

    const stateName = {
        open: "membuka",
        close: "menutup",
        open_auto: "membuka otomatis",
    }[state];

    await gatewayStore.msgPut({
        rid: doorId,
        deviceId: doorId,
        uid: this.user.uid,
        txt: `${userName} ${stateName} ${deviceName}`,
    });
});
