import type { ServerWebSocket } from "bun";
import { $fnRun } from "../fn";
import { WSHandlerAuth as WSHandlerUser } from "./parts/ws.handler.user";
import { WSHandlerVars } from "./parts/ws.handler.vars";
import {
    computeSysUsage,
    cSerialize,
    MSG_INVOKE,
    MSG_INVOKE_RESULT,
    MSG_SYS_USAGE,
    type SysUsage,
    type SysUsageComputed,
} from "cm_net";

export interface ServerWSData {
    url: URL;
    handlerType: string;
    headers: Headers;
    handler?: WSHandler;
}

export const peers: { [sid: string]: Set<WSHandler> } = {};

export abstract class WSHandler {
    readonly ws: ServerWebSocket<ServerWSData>;
    readonly sid!: string;
    readonly shardId!: string;

    readonly user = new WSHandlerUser(this);
    readonly vars = new WSHandlerVars(this);

    readonly ready!: Promise<void>;

    #sysUsage: SysUsageComputed | undefined;
    #sysUsageTs: Date | undefined;

    readonly connectedAt: Date = new Date();
    lastActivityAt: Date = new Date();

    get sysUsage(): SysUsageComputed | undefined {
        // if more than 15s, set to undefined
        if (
            this.#sysUsageTs && Date.now() - this.#sysUsageTs.getTime() > 15000
        ) {
            this.#sysUsage = undefined;
        }
        return this.#sysUsage;
    }

    set sysUsage(sysUsage: SysUsageComputed | undefined) {
        this.#sysUsage = sysUsage;
        this.#sysUsageTs = new Date();
    }

    constructor(ws: ServerWebSocket<ServerWSData>) {
        this.ws = ws;
        const [, type, sidPart] = decodeURIComponent(ws.data.url.pathname)
            .split("/");
        let [sid, shardId] = sidPart.split("|");
        if (!shardId) shardId = "";

        ws.data.handler = this;
        const sidPeers = peers[sid] ??= new Set();

        // if already connected, disconnect old
        // if (sidPeers[shardId]) {
        //     sidPeers[shardId].ws.close();
        //     l("Session exists, closing old:", sid, shardId);
        // ws.sendText(`Session ${sid}:${shardId} already exists`);
        // return;
        // }

        // set sid & shardId
        sidPeers.add(this);
        this.sid = sid;
        this.shardId = shardId;

        // Get real client IP from Cloudflare headers
        let clientIP = ws.remoteAddress;

        // Try Cloudflare-specific header first
        const cfIP = ws.data.headers.get("CF-Connecting-IP");
        if (cfIP) {
            clientIP = cfIP;
        } else {
            // Try standard X-Forwarded-For header
            const forwardedFor = ws.data.headers.get("X-Forwarded-For");
            if (forwardedFor) {
                // X-Forwarded-For can contain multiple IPs, the first one is the client
                clientIP = forwardedFor.split(",")[0].trim();
            }
        }

        console.log("Connected:", sid, clientIP);

        this.ready = (async () => {
            await this.vars.load();

            // send uid
            this.user.sendCurrentUser();
        })();
    }

    /** Send to currently connected socket */
    send(cmd: number, segments: any[]) {
        this.ws.send(JSON.stringify(cSerialize([cmd, ...segments])));
    }

    async handleMsg(type: number, args: any[]) {
        this.lastActivityAt = new Date();
        // console.log('handle msg', type, args);

        // trigger onMessage
        this.onMessage(type, args);

        // if invoke
        switch (type) {
            case MSG_INVOKE: {
                let [peerId, rid, fn, fnArgs] = args;
                fnArgs ??= [];
                console.log("[Invoke]", peerId, rid, fn, fnArgs);

                // if peerId = '', then it's a local call
                if (peerId === "") {
                    try {
                        const result = await $fnRun(this, fn, fnArgs);
                        this.send(MSG_INVOKE_RESULT, [rid, result]);
                    } catch (ex) {
                        console.log("Sending Error", ex);
                        const errorMsg = {
                            msg: typeof ex === "object"
                                ? (ex as any)?.message
                                : ex,
                        };

                        this.send(MSG_INVOKE_RESULT, [
                            rid,
                            null,
                            errorMsg,
                        ]);
                    }
                    return;
                } // else forward to peer
                else {
                    // need to handle invoke result here, because on local call, rid is generated on wsHandler
                    // and invoke result will be sent directly to client,
                    // this is correct, but the result is not returned!
                    this.invokePeer(peerId, fn, fnArgs, rid);
                }

                break;
            }

            // invoke result form peer, just forward it
            case MSG_INVOKE_RESULT: {
                const [sid, rid, result, error] = args;
                console.log("[Invoke Result]", sid, rid, result, error);
                const peer = this.getPeer(sid);
                if (!peer) break;

                peer.send(MSG_INVOKE_RESULT, [rid, result, error]);
                break;
            }

            case MSG_SYS_USAGE: {
                const sysUsage = args[0];
                const computedSysUsage = computeSysUsage(sysUsage);
                this.sysUsage = computedSysUsage;
                break;
            }
        }
    }

    handleClose() {
        const sidPeers = peers[this.sid];
        if (!sidPeers) return;

        sidPeers.delete(this);

        if (sidPeers.size === 0) {
            delete peers[this.sid];
        }
        console.log("Disconnected:", `${this.sid}:${this.shardId}`);
        this.onClose();
    }

    abstract onMessage(type: number, ...args: any[]): Promise<void>;

    onClose() {
        this.vars.set("_online", false);
    }

    // ------------------------ peer utils
    getPeer(sid: string): WSHandler | undefined {
        const sidPeersMap = peers[sid];
        if (!sidPeersMap) return undefined;

        const sidPeers = [...sidPeersMap];
        if (sidPeers.length === 0) return undefined;

        // TODO: use lowest load
        sidPeers.sort((a, b) => {
            const aLoad = a.sysUsage?._ ?? Number.MAX_SAFE_INTEGER;
            const bLoad = b.sysUsage?._ ?? Number.MAX_SAFE_INTEGER;
            return aLoad - bLoad;
        });

        const peerIdx = Math.floor(Math.random() * sidPeers.length);

        const peer = sidPeers[peerIdx];
        return peer;
    }

    lastRid: number = 0;
    sendToPeer(peerId: string, cmd: number, ...args: any[]) {
        // TODO: use redis instead of directly sending
        const peer = this.getPeer(peerId);
        if (!peer) return;

        peer.send(cmd, args);
    }

    invokePeer(
        toPeerId: string,
        fn: string,
        args: any[],
        rid: number = ++this.lastRid,
    ) {
        const fromPeerId: string = this.sid;
        const peer = this.getPeer(toPeerId);
        if (!peer) {
            this.send(MSG_INVOKE_RESULT, [rid, null, {
                msg: `Peer not found: ${toPeerId}`,
            }]);
        } else {
            peer.send(MSG_INVOKE, [fromPeerId, rid, fn, args]);
        }
    }
}
