apiVersion: apps/v1
kind: Deployment
metadata:
  name: test-nginx
  namespace: docker-registry
spec:
  replicas: 1
  selector:
    matchLabels:
      app: test-nginx
  template:
    metadata:
      labels:
        app: test-nginx
    spec:
      containers:
      - name: nginx
        image: nginx:alpine
        ports:
        - containerPort: 80
---
apiVersion: v1
kind: Service
metadata:
  name: test-nginx
  namespace: docker-registry
spec:
  selector:
    app: test-nginx
  ports:
  - port: 80
    targetPort: 80
---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: test-nginx-ingressroute
  namespace: docker-registry
spec:
  entryPoints:
    - web
  routes:
    - match: Host(`test.alienai.id`)
      kind: Rule
      services:
        - name: test-nginx
          port: 80
